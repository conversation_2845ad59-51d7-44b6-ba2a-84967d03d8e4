"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"

import { BookCard } from "@/components/BookCard"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  tags: string[]
  user_id: string
  created_at: string
  slug: string
  author_name?: string
  users: {
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
}

function BooksPageContent() {
  const searchParams = useSearchParams()
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGenre, setSelectedGenre] = useState<string>("all")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("popular")
  const [searchQuery, setSearchQuery] = useState<string>(searchParams.get('search') || "")
  const [isScrolled, setIsScrolled] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [showSignUpPrompt, setShowSignUpPrompt] = useState(false)
  const [viewedBooks, setViewedBooks] = useState(0)
  const supabase = createSupabaseClient()

  const genres = [
    "all",
    "fiction",
    "non_fiction",
    "memoir",
    "poetry",
    "romance",
    "mystery",
    "thriller",
    "fantasy",
    "science_fiction",
    "horror",
    "young_adult",
    "children",
    "biography",
    "self_help",
    "business",
    "health",
    "history",
    "travel",
    "cooking",
    "art",
    "religion",
    "philosophy",
    "african_american_fiction",
    "urban_fiction",
    "street_fiction",
    "other"
  ]

  // Function to format genre names for display
  const formatGenreName = (genre: string) => {
    const genreMap: { [key: string]: string } = {
      'african_american_fiction': 'African American Fiction',
      'urban_fiction': 'Urban Fiction',
      'street_fiction': 'Street Fiction',
      'non_fiction': 'Non-Fiction',
      'science_fiction': 'Science Fiction',
      'young_adult': 'Young Adult',
      'self_help': 'Self-Help'
    }

    return genreMap[genre] || genre.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }
  const bookTypes = ["all", "fiction", "non_fiction", "memoir", "poetry", "other"]
  const sortOptions = [
    { value: "popular", label: "🔥 Most Popular" },
    { value: "bestsellers", label: "📈 Bestsellers" },
    { value: "newest", label: "🆕 Newest" },
    { value: "highest_rated", label: "⭐ Highest Rated" },
    { value: "price_low", label: "💰 Price: Low to High" },
    { value: "price_high", label: "💎 Price: High to Low" }
  ]

  useEffect(() => {
    fetchBooks()
  }, [selectedGenre, selectedType, sortBy, searchQuery])

  // Check for user authentication
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { user: authUser } } = await supabase.auth.getUser()
        setUser(authUser)
      } catch (error) {
        console.log('Books: No user session, showing public content')
        setUser(null)
      }
    }
    checkAuth()
  }, [])

  // Track scroll position for header behavior
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      setIsScrolled(scrollTop > 50) // Trigger after scrolling 50px
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Track book views for anonymous users
  const handleBookClick = (bookId: string) => {
    if (!user) {
      const newViewCount = viewedBooks + 1
      setViewedBooks(newViewCount)

      // Show sign-up prompt after viewing 3 books
      if (newViewCount >= 3) {
        setShowSignUpPrompt(true)
      }
    }
  }

  const fetchBooks = async () => {
    setLoading(true)
    try {
      console.log('Fetching books with filters:', { selectedGenre, selectedType, sortBy })
      let query = supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          cover_image_url,
          genre,
          book_type,
          price_amount,
          average_rating,
          review_count,
          sales_count,
          tags,
          slug,
          user_id,
          created_at,
          author_name,
          users!inner(name, avatar, profile_picture_url, has_day1_badge, signup_number, badge_tier)
        `)
        .eq('is_ebook', true)
        .eq('is_complete', true)
        .or('price_amount.gte.0,price_amount.is.null')
        .eq('is_private', false)

      // Apply search filter
      if (searchQuery.trim()) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,author_name.ilike.%${searchQuery}%`)
      }

      // Apply filters
      if (selectedGenre !== "all") {
        query = query.eq('genre', selectedGenre)
      }
      if (selectedType !== "all") {
        query = query.eq('book_type', selectedType)
      }

      // Apply sorting
      switch (sortBy) {
        case "popular":
          // Sort by a combination of sales, ratings, and recency for popularity
          query = query.order('sales_count', { ascending: false })
            .order('average_rating', { ascending: false })
            .order('created_at', { ascending: false })
          break
        case "bestsellers":
          query = query.order('sales_count', { ascending: false })
          break
        case "newest":
          query = query.order('created_at', { ascending: false })
          break
        case "highest_rated":
          query = query.order('average_rating', { ascending: false })
          break
        case "price_low":
          query = query.order('price_amount', { ascending: true })
          break
        case "price_high":
          query = query.order('price_amount', { ascending: false })
          break
        default:
          // Default to popular sorting
          query = query.order('sales_count', { ascending: false })
            .order('average_rating', { ascending: false })
            .order('created_at', { ascending: false })
          break
      }

      console.log('Executing query with filters:', { selectedGenre, selectedType, sortBy })

      const { data, error } = await query.limit(50)

      if (error) {
        console.error('Supabase query error:', error)
        console.error('Error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        })
        throw error
      }

      console.log('✅ Fetched books data:', data?.length, 'books')
      console.log('📊 Query filters applied:', {
        is_ebook: true,
        is_complete: true,
        is_private: false,
        price_amount_gte: 0,
        selectedGenre,
        selectedType,
        sortBy
      })
      if (data && data.length > 0) {
        console.log('Sample book:', data[0])
      }

      // Process books data with fallbacks for missing fields
      const processedBooks = data?.map(book => ({
        ...book,
        book_type: book.book_type || book.genre || 'fiction',
        average_rating: book.average_rating || 0,
        review_count: book.review_count || 0,
        sales_count: book.sales_count || 0,
        tags: book.tags || [],
        slug: book.slug || book.title?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') || book.id,
        author_name: book.author_name || book.users?.name || 'Unknown Author',
        // Preserve user data including profile pictures
        users: {
          name: book.author_name || book.users?.name || 'Unknown Author',
          avatar: book.users?.avatar || null,
          profile_picture_url: book.users?.profile_picture_url || null,
          has_day1_badge: book.users?.has_day1_badge || false,
          signup_number: book.users?.signup_number || null,
          badge_tier: book.users?.badge_tier || null
        }
      })) || []

      setBooks(processedBooks)
    } catch (error) {
      console.error('Error fetching books:', error)
      console.error('Error details:', {
        message: error?.message,
        code: error?.code,
        details: error?.details,
        hint: error?.hint,
        filters: { selectedGenre, selectedType, sortBy }
      })
      // Set empty array on error to prevent UI issues
      setBooks([])
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const renderStars = (rating: number) => {
    const pens = Math.round(rating)
    return "🖊️".repeat(pens) + "🖊️".repeat(Math.max(0, 10 - pens)).replace(/🖊️/g, "✏️")
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
            <div className="text-center">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-serif text-gray-900 mb-3 sm:mb-4">
                📚 OnlyDiary Books
              </h1>
              <p className="text-sm sm:text-lg text-gray-600 max-w-2xl mx-auto px-2">
                Discover authentic stories from real authors. Every book supports independent writers directly.
              </p>
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            {/* Enhanced Loading Spinner */}
            <div className="relative inline-flex items-center justify-center mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent absolute"></div>
              <div className="absolute text-2xl">📚</div>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Books...</h2>
            <p className="text-gray-600">Discovering amazing stories for you</p>

            {/* Loading skeleton cards */}
            <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse">
                  <div className="aspect-[3/4] bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 books-page">
      {/* Compact Header - Only visible when not scrolled */}
      <div className={`bg-white border-b border-gray-200 transition-all duration-300 ${isScrolled ? 'hidden' : 'block'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="text-center">
            <h1 className="text-xl sm:text-2xl font-serif text-gray-900 mb-2">
              📚 OnlyDiary Books
            </h1>
            <p className="text-xs sm:text-sm text-gray-600">
              Discover authentic stories from real authors
            </p>
          </div>
        </div>
      </div>

      {/* Search & Filters - Sticky header that adapts to scroll */}
      <div className="bg-white border-b border-gray-200 sticky top-16 z-40 shadow-sm transition-all duration-300">
        <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ${isScrolled ? 'py-2' : 'py-3'}`}>
          <div className={`flex flex-col transition-all duration-300 ${isScrolled ? 'gap-0' : 'gap-3'}`}>

            {/* Search Bar */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-400 text-lg">🔍</span>
              </div>
              <input
                type="text"
                placeholder="Search books..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-gray-500 transition-all duration-300 ${
                  isScrolled ? 'py-1.5' : 'py-2'
                }`}
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <span className="text-lg">✕</span>
                </button>
              )}
            </div>

            {/* Filters Row with Book Count - Hidden when scrolled */}
            <div className={`flex flex-wrap gap-2 items-center justify-between transition-all duration-300 overflow-hidden ${
              isScrolled ? 'max-h-0 opacity-0' : 'max-h-20 opacity-100'
            }`}>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>{books.length} books</span>
              </div>
              <div className="flex flex-wrap gap-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 min-h-[44px]"
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedGenre}
                  onChange={(e) => setSelectedGenre(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 min-h-[44px]"
                >
                  <option value="all">All Genres</option>
                  {genres.slice(1).map(genre => (
                    <option key={genre} value={genre}>
                      {formatGenreName(genre)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Results count and refresh */}
              <div className="flex items-center gap-3">
                <div className="text-sm text-gray-500">
                  {books.length} book{books.length !== 1 ? 's' : ''} found
                </div>
                <button
                  onClick={() => fetchBooks()}
                  className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors flex items-center gap-1"
                  title="Refresh books list"
                >
                  <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Refresh
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Books Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4 pb-8">
        {loading ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {[...Array(10)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-[3/4] bg-gray-200 rounded-t-lg"></div>
                <CardContent className="p-3">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : books.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No books found</h3>
            <p className="text-gray-600">Try adjusting your filters or check back later for new releases.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {books.map((book, index) => (
              <BookCard
                key={book.id}
                book={book}
                priority={index < 4} // Prioritize loading first 4 images
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default function BooksPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 books-page">
        {/* Hero Section */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
            <div className="text-center">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-serif text-gray-900 mb-3 sm:mb-4">
                📚 OnlyDiary Books
              </h1>
              <p className="text-sm sm:text-lg text-gray-600 max-w-2xl mx-auto px-2">
                Discover authentic stories from real authors. Every book supports independent writers directly.
              </p>
            </div>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-[3/4] bg-gray-200 rounded-t-lg"></div>
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    }>
      <BooksPageContent />
    </Suspense>
  )
}
