'use client'

import { useEffect } from 'react'

/**
 * Component to fix mobile Safari touch event issues in production
 * This addresses the specific problem where touch events don't work
 * on mobile Safari in production but work fine in development
 */
export function MobileSafariTouchFix() {
  useEffect(() => {
    // Only run on mobile Safari
    const isMobileSafari = /iPhone|iPad|iPod/i.test(navigator.userAgent) && 
                          /Safari/i.test(navigator.userAgent) && 
                          !/Chrome|CriOS|FxiOS/i.test(navigator.userAgent)
    
    if (!isMobileSafari) return

    console.log('🔧 Applying mobile Safari touch fixes...')

    // Fix for hamburger menu buttons
    const fixMobileMenuButtons = () => {
      const mobileMenuButtons = document.querySelectorAll('[data-mobile-menu-trigger]')
      
      mobileMenuButtons.forEach((button) => {
        if (button instanceof HTMLElement) {
          // Remove existing listeners to prevent duplicates
          const newButton = button.cloneNode(true) as HTMLElement
          button.parentNode?.replaceChild(newButton, button)
          
          // Add touch event listeners
          newButton.addEventListener('touchstart', (e) => {
            e.preventDefault()
            e.stopPropagation()
            newButton.click()
          }, { passive: false })
          
          newButton.addEventListener('touchend', (e) => {
            e.preventDefault()
          }, { passive: false })
          
          // Ensure proper styling
          newButton.style.touchAction = 'manipulation'
          newButton.style.webkitTapHighlightColor = 'transparent'
          newButton.style.pointerEvents = 'auto'
          newButton.style.cursor = 'pointer'
        }
      })
    }

    // Fix for tutorial trigger buttons
    const fixTutorialButtons = () => {
      const tutorialButtons = document.querySelectorAll('[data-tutorial-trigger]')
      
      tutorialButtons.forEach((button) => {
        if (button instanceof HTMLElement) {
          // Remove existing listeners to prevent duplicates
          const newButton = button.cloneNode(true) as HTMLElement
          button.parentNode?.replaceChild(newButton, button)
          
          // Add touch event listeners
          newButton.addEventListener('touchstart', (e) => {
            e.preventDefault()
            e.stopPropagation()
            newButton.click()
          }, { passive: false })
          
          newButton.addEventListener('touchend', (e) => {
            e.preventDefault()
          }, { passive: false })
          
          // Ensure proper styling
          newButton.style.touchAction = 'manipulation'
          newButton.style.webkitTapHighlightColor = 'transparent'
          newButton.style.pointerEvents = 'auto'
          newButton.style.cursor = 'pointer'
        }
      })
    }

    // Apply fixes immediately
    fixMobileMenuButtons()
    fixTutorialButtons()

    // Re-apply fixes when DOM changes (for dynamic content)
    const observer = new MutationObserver((mutations) => {
      let shouldReapplyFixes = false
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLElement) {
              if (node.querySelector('[data-mobile-menu-trigger], [data-tutorial-trigger]') ||
                  node.hasAttribute('data-mobile-menu-trigger') ||
                  node.hasAttribute('data-tutorial-trigger')) {
                shouldReapplyFixes = true
              }
            }
          })
        }
      })
      
      if (shouldReapplyFixes) {
        setTimeout(() => {
          fixMobileMenuButtons()
          fixTutorialButtons()
        }, 100)
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // Cleanup
    return () => {
      observer.disconnect()
    }
  }, [])

  // This component doesn't render anything
  return null
}
