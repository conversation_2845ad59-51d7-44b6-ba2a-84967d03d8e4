'use client'

import { useEffect } from 'react'

/**
 * Component to fix mobile touch event issues in production
 * This addresses touch events not working on mobile devices in production
 */
export function MobileTouchFix() {
  useEffect(() => {
    // Run on all mobile devices, not just Safari
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                     window.innerWidth < 768 ||
                     ('ontouchstart' in window) ||
                     (navigator.maxTouchPoints > 0)

    if (!isMobile) return

    console.log('🔧 Applying mobile touch fixes...')

    // Fix for hamburger menu buttons
    const fixMobileMenuButtons = () => {
      const mobileMenuButtons = document.querySelectorAll('[data-mobile-menu-trigger]')

      mobileMenuButtons.forEach((button) => {
        if (button instanceof HTMLElement) {
          // Skip if already fixed
          if (button.hasAttribute('data-touch-fixed')) return

          // Mark as fixed
          button.setAttribute('data-touch-fixed', 'true')

          // Add comprehensive touch event listeners
          const handleTouch = (e: TouchEvent) => {
            e.preventDefault()
            e.stopPropagation()

            // Trigger click on touchstart for immediate response
            if (e.type === 'touchstart') {
              button.click()
            }
          }

          button.addEventListener('touchstart', handleTouch, { passive: false })
          button.addEventListener('touchend', (e) => e.preventDefault(), { passive: false })
          button.addEventListener('touchcancel', (e) => e.preventDefault(), { passive: false })

          // Ensure proper styling for mobile
          button.style.touchAction = 'manipulation'
          button.style.webkitTapHighlightColor = 'transparent'
          button.style.pointerEvents = 'auto'
          button.style.cursor = 'pointer'
          button.style.userSelect = 'none'
          button.style.webkitUserSelect = 'none'

          // Force hardware acceleration
          button.style.transform = 'translateZ(0)'
          button.style.willChange = 'transform'
        }
      })
    }

    // Fix for tutorial trigger buttons
    const fixTutorialButtons = () => {
      const tutorialButtons = document.querySelectorAll('[data-tutorial-trigger], .tutorial-trigger')

      tutorialButtons.forEach((button) => {
        if (button instanceof HTMLElement) {
          // Skip if already fixed
          if (button.hasAttribute('data-touch-fixed')) return

          // Mark as fixed
          button.setAttribute('data-touch-fixed', 'true')

          // Add comprehensive touch event listeners
          const handleTouch = (e: TouchEvent) => {
            e.preventDefault()
            e.stopPropagation()

            // Trigger click on touchstart for immediate response
            if (e.type === 'touchstart') {
              button.click()
            }
          }

          button.addEventListener('touchstart', handleTouch, { passive: false })
          button.addEventListener('touchend', (e) => e.preventDefault(), { passive: false })
          button.addEventListener('touchcancel', (e) => e.preventDefault(), { passive: false })

          // Ensure proper styling for mobile
          button.style.touchAction = 'manipulation'
          button.style.webkitTapHighlightColor = 'transparent'
          button.style.pointerEvents = 'auto'
          button.style.cursor = 'pointer'
          button.style.userSelect = 'none'
          button.style.webkitUserSelect = 'none'

          // Force hardware acceleration
          button.style.transform = 'translateZ(0)'
          button.style.willChange = 'transform'
        }
      })
    }

    // Fix for all mobile navigation buttons
    const fixAllMobileButtons = () => {
      const allButtons = document.querySelectorAll('nav button, .mobile-menu-button, button[class*="mobile"]')

      allButtons.forEach((button) => {
        if (button instanceof HTMLElement) {
          // Skip if already fixed
          if (button.hasAttribute('data-touch-fixed')) return

          // Mark as fixed
          button.setAttribute('data-touch-fixed', 'true')

          // Add comprehensive touch event listeners
          const handleTouch = (e: TouchEvent) => {
            e.preventDefault()
            e.stopPropagation()

            // Trigger click on touchstart for immediate response
            if (e.type === 'touchstart') {
              button.click()
            }
          }

          button.addEventListener('touchstart', handleTouch, { passive: false })
          button.addEventListener('touchend', (e) => e.preventDefault(), { passive: false })
          button.addEventListener('touchcancel', (e) => e.preventDefault(), { passive: false })

          // Ensure proper styling for mobile
          button.style.touchAction = 'manipulation'
          button.style.webkitTapHighlightColor = 'transparent'
          button.style.pointerEvents = 'auto'
          button.style.cursor = 'pointer'
          button.style.userSelect = 'none'
          button.style.webkitUserSelect = 'none'

          // Force hardware acceleration
          button.style.transform = 'translateZ(0)'
          button.style.willChange = 'transform'
        }
      })
    }

    // Apply fixes immediately
    fixMobileMenuButtons()
    fixTutorialButtons()
    fixAllMobileButtons()

    // Re-apply fixes when DOM changes (for dynamic content)
    const observer = new MutationObserver((mutations) => {
      let shouldReapplyFixes = false
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLElement) {
              if (node.querySelector('[data-mobile-menu-trigger], [data-tutorial-trigger]') ||
                  node.hasAttribute('data-mobile-menu-trigger') ||
                  node.hasAttribute('data-tutorial-trigger')) {
                shouldReapplyFixes = true
              }
            }
          })
        }
      })
      
      if (shouldReapplyFixes) {
        setTimeout(() => {
          fixMobileMenuButtons()
          fixTutorialButtons()
          fixAllMobileButtons()
        }, 100)
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    // Cleanup
    return () => {
      observer.disconnect()
    }
  }, [])

  // This component doesn't render anything
  return null
}
